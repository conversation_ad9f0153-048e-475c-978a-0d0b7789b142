#!/usr/bin/env python3
"""
Script para comparar estratégias de investimento usando MM, Butterworth e XGBoost
Simula investimento usando sinais das 3 metodologias com parâmetros configuráveis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import ConfigLoader
from config_loader import config, setup_environment

def carregar_dados_mm():
    """
    Carrega dados históricos das médias móveis dos arquivos individuais
    """
    try:
        individual_dir = 'results/csv/mm_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório MM não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('mm_series_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo MM encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('mm_series_', '').replace('.csv', '')
            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Criar sinais baseados nas médias móveis
                # Sinal de compra: preço cruza acima da MM25
                df_acao['Sinal_Compra'] = ((df_acao['Preco_Media_OHLC'] > df_acao['MM25_dias']) &
                                          (df_acao['Preco_Media_OHLC'].shift(1) <= df_acao['MM25_dias'].shift(1))).astype(int)

                # Sinal de venda: preço cruza abaixo da MM25
                df_acao['Sinal_Venda'] = ((df_acao['Preco_Media_OHLC'] < df_acao['MM25_dias']) &
                                         (df_acao['Preco_Media_OHLC'].shift(1) >= df_acao['MM25_dias'].shift(1))).astype(int)

                # Renomear coluna para compatibilidade
                df_acao['Close'] = df_acao['Preco_Media_OHLC']

                dados_completos.append(df_acao)

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ MM: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado MM válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados MM: {e}")
        return None

def carregar_dados_butterworth():
    """
    Carrega dados históricos do filtro Butterworth dos arquivos individuais
    """
    try:
        individual_dir = 'results/csv/butterworth_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório Butterworth não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('butterworth_series_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo Butterworth encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('butterworth_series_', '').replace('.csv', '')
            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Criar sinais baseados no filtro Butterworth
                # Sinal de compra: preço cruza acima do Butterworth_25_dias
                df_acao['Sinal_Compra'] = ((df_acao['Preco_Media_OHLC'] > df_acao['Butterworth_25_dias']) &
                                          (df_acao['Preco_Media_OHLC'].shift(1) <= df_acao['Butterworth_25_dias'].shift(1))).astype(int)

                # Sinal de venda: preço cruza abaixo do Butterworth_25_dias
                df_acao['Sinal_Venda'] = ((df_acao['Preco_Media_OHLC'] < df_acao['Butterworth_25_dias']) &
                                         (df_acao['Preco_Media_OHLC'].shift(1) >= df_acao['Butterworth_25_dias'].shift(1))).astype(int)

                # Renomear coluna para compatibilidade
                df_acao['Close'] = df_acao['Preco_Media_OHLC']

                dados_completos.append(df_acao)

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ Butterworth: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado Butterworth válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados Butterworth: {e}")
        return None

def carregar_dados_xgboost():
    """
    Carrega dados históricos do XGBoost dos arquivos individuais
    """
    try:
        individual_dir = 'results/csv/xgboost_analysis/individual_stocks'
        if not os.path.exists(individual_dir):
            print(f"❌ Diretório XGBoost não encontrado: {individual_dir}")
            return None

        # Listar arquivos individuais
        arquivos = [f for f in os.listdir(individual_dir) if f.startswith('xgboost_') and f.endswith('.csv')]

        if not arquivos:
            print(f"❌ Nenhum arquivo XGBoost encontrado em: {individual_dir}")
            return None

        dados_completos = []

        for arquivo in arquivos:
            ticker = arquivo.replace('xgboost_', '').replace('.csv', '')
            caminho_arquivo = os.path.join(individual_dir, arquivo)

            try:
                df_acao = pd.read_csv(caminho_arquivo)
                df_acao['Data'] = pd.to_datetime(df_acao['Data'])
                df_acao['Ticker'] = ticker

                # Verificar se as colunas necessárias existem
                # Priorizar predições binárias do novo modelo XGBoost
                if 'Pred_Compra' in df_acao.columns and 'Pred_Venda' in df_acao.columns:
                    # Usar predições binárias do modelo XGBoost (novo formato)
                    df_acao['Sinal_Compra'] = df_acao['Pred_Compra']
                    df_acao['Sinal_Venda'] = df_acao['Pred_Venda']
                    print(f"   ✅ Usando PREDIÇÕES XGBoost binárias para {ticker}")
                elif 'Pred_Multiclass' in df_acao.columns:
                    # CompPatibilidade com modelo multiclasse antigo (se existir)
                    # Converter predições multiclasse para formato binário
                    df_acao['Sinal_Compra'] = (df_acao['Pred_Multiclass'] == 1).astype(int)
                    df_acao['Sinal_Venda'] = (df_acao['Pred_Multiclass'] == 2).astype(int)
                    print(f"   ✅ Usando PREDIÇÕES XGBoost multiclasse (compatibilidade) para {ticker}")
                elif 'Sinal_Compra' in df_acao.columns and 'Sinal_Venda' in df_acao.columns:
                    # Usar sinais de treinamento como fallback
                    print(f"   ⚠️ Usando sinais de treinamento para {ticker} (predições não disponíveis)")
                else:
                    print(f"   ❌ Nem predições nem sinais encontrados em {arquivo}")
                    continue

                if 'Media_OHLC' in df_acao.columns:
                    # Renomear coluna para compatibilidade com simulação
                    df_acao['Close'] = df_acao['Media_OHLC']
                    dados_completos.append(df_acao)
                else:
                    print(f"   ⚠️ Coluna Media_OHLC não encontrada em {arquivo}")

            except Exception as e:
                print(f"   ⚠️ Erro ao carregar {arquivo}: {e}")

        if dados_completos:
            df_final = pd.concat(dados_completos, ignore_index=True)
            print(f"✅ XGBoost: {len(df_final)} registros carregados de {len(dados_completos)} ações")
            return df_final
        else:
            print(f"❌ Nenhum dado XGBoost válido carregado")
            return None

    except Exception as e:
        print(f"❌ Erro ao carregar dados XGBoost: {e}")
        return None

def filtrar_periodo_simulacao(df, data_inicio=None):
    """
    Filtra dados para período de simulação configurado
    """
    # Carregar configuração
    config_loader = ConfigLoader()
    periodo_config = config_loader.get_simulation_period('comparison')

    # Converter período para dias
    if periodo_config == '1y':
        dias_periodo = 365
    elif periodo_config == '6mo':
        dias_periodo = 180
    elif periodo_config == '2y':
        dias_periodo = 730
    else:
        # Padrão: 1 ano
        dias_periodo = 365

    if data_inicio is None:
        # Usar últimos N meses/anos disponíveis
        data_fim = df['Data'].max()
        data_inicio = data_fim - timedelta(days=dias_periodo)
    else:
        data_fim = data_inicio + timedelta(days=dias_periodo)

    df_filtrado = df[(df['Data'] >= data_inicio) & (df['Data'] <= data_fim)].copy()
    print(f"   📅 Período: {data_inicio.strftime('%Y-%m-%d')} a {data_fim.strftime('%Y-%m-%d')}")
    print(f"   📊 Registros no período: {len(df_filtrado)}")
    print(f"   ⏱️ Duração configurada: {periodo_config}")

    return df_filtrado, data_inicio, data_fim

def simular_investimento(df, metodologia, capital_inicial=None):
    """
    Simula investimento seguindo sinais de uma metodologia
    Com sinal de compra: compra quantidade fixa de ações (se capital permitir)
    Com sinal de venda: vende todas as ações disponíveis da ação indicada
    """
    # Carregar configuração se capital não foi especificado
    if capital_inicial is None:
        config_loader = ConfigLoader()
        capital_inicial = config_loader.get_initial_capital('comparison')

    # Carregar quantidade fixa de ações para compra
    config_loader = ConfigLoader()
    quantidade_fixa = config_loader.config.get('simulation', {}).get('trading', {}).get('fixed_quantity_per_stock', 100)

    print(f"\n💰 Simulando investimento - {metodologia}")
    print(f"   💵 Capital inicial: R$ {capital_inicial:.2f}")
    print(f"   📊 Quantidade fixa por compra: {quantidade_fixa} ações")

    # Inicializar variáveis
    capital_disponivel = capital_inicial
    posicoes = {}  # {ticker: {'quantidade': X, 'preco_compra': Y}}
    historico_transacoes = []
    historico_capital = []

    # Determinar colunas de sinais baseado na metodologia
    # XGBoost usa predições (Pred_Compra/Pred_Venda convertidas para Sinal_Compra/Sinal_Venda)
    # MM e Butterworth usam sinais calculados baseados em cruzamentos
    col_compra = 'Sinal_Compra'
    col_venda = 'Sinal_Venda'
    col_preco = 'Close'  # Todas usam Close (XGBoost foi convertido na carga)
    
    # Processar sinais dia a dia
    for _, row in df.iterrows():
        ticker = row['Ticker']
        data = row['Data']
        preco = row[col_preco]
        sinal_compra = row[col_compra] if col_compra in row else 0
        sinal_venda = row[col_venda] if col_venda in row else 0

        # Processar sinal de venda primeiro - VENDER TODAS AS AÇÕES DISPONÍVEIS
        if sinal_venda == 1 and ticker in posicoes:
            quantidade = posicoes[ticker]['quantidade']
            valor_venda = quantidade * preco

            capital_disponivel += valor_venda

            historico_transacoes.append({
                'Data': data,
                'Ticker': ticker,
                'Tipo': 'Venda',
                'Quantidade': quantidade,
                'Preco': preco,
                'Valor': valor_venda,
                'Capital_Pos': capital_disponivel
            })

            del posicoes[ticker]

        # Processar sinal de compra - COMPRAR QUANTIDADE FIXA (se capital permitir)
        elif sinal_compra == 1 and ticker not in posicoes:
            # Calcular valor necessário para comprar quantidade fixa
            valor_necessario = quantidade_fixa * preco

            if capital_disponivel >= valor_necessario:  # Verificar se tem dinheiro suficiente
                capital_disponivel -= valor_necessario
                posicoes[ticker] = {
                    'quantidade': quantidade_fixa,
                    'preco_compra': preco
                }

                historico_transacoes.append({
                    'Data': data,
                    'Ticker': ticker,
                    'Tipo': 'Compra',
                    'Quantidade': quantidade_fixa,
                    'Preco': preco,
                    'Valor': valor_necessario,
                    'Capital_Pos': capital_disponivel
                })
        
        # Calcular valor total do portfólio
        valor_posicoes = sum(pos['quantidade'] * preco for pos in posicoes.values())
        capital_total = capital_disponivel + valor_posicoes
        
        historico_capital.append({
            'Data': data,
            'Capital_Disponivel': capital_disponivel,
            'Valor_Posicoes': valor_posicoes,
            'Capital_Total': capital_total
        })
    
    # Calcular resultado final
    valor_final_posicoes = 0
    for ticker, pos in posicoes.items():
        # Usar último preço disponível para cada ticker
        ultimo_preco = df[df['Ticker'] == ticker][col_preco].iloc[-1]
        valor_final_posicoes += pos['quantidade'] * ultimo_preco
    
    capital_final = capital_disponivel + valor_final_posicoes
    rendimento_absoluto = capital_final - capital_inicial
    rendimento_percentual = (rendimento_absoluto / capital_inicial) * 100
    
    resultado = {
        'metodologia': metodologia,
        'capital_inicial': capital_inicial,
        'capital_final': capital_final,
        'capital_disponivel_final': capital_disponivel,
        'valor_posicoes_final': valor_final_posicoes,
        'rendimento_absoluto': rendimento_absoluto,
        'rendimento_percentual': rendimento_percentual,
        'num_transacoes': len(historico_transacoes),
        'num_posicoes_finais': len(posicoes),
        'historico_transacoes': historico_transacoes,
        'historico_capital': historico_capital
    }
    
    print(f"   💵 Capital Final: R$ {capital_final:.2f}")
    print(f"   📈 Rendimento: R$ {rendimento_absoluto:.2f} ({rendimento_percentual:.2f}%)")
    print(f"   🔄 Transações: {len(historico_transacoes)}")
    print(f"   📊 Posições finais: {len(posicoes)}")
    
    return resultado

def criar_graficos_comparacao(resultados):
    """
    Cria gráficos comparando as 3 metodologias
    """
    print(f"\n📊 Criando gráficos de comparação...")

    # Carregar configuração para títulos dinâmicos
    config_loader = ConfigLoader()
    capital_inicial = config_loader.get_initial_capital('comparison')
    periodo = config_loader.get_simulation_period('comparison')

    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'Comparação de Estratégias de Investimento - R$ {capital_inicial:.0f} por {periodo}', fontsize=16, fontweight='bold')
    
    metodologias = [r['metodologia'] for r in resultados]
    cores = ['blue', 'green', 'red']
    
    # 1. Capital Final
    capitals_finais = [r['capital_final'] for r in resultados]
    bars1 = axes[0,0].bar(metodologias, capitals_finais, color=cores, alpha=0.7)
    axes[0,0].set_title('Capital Final')
    axes[0,0].set_ylabel('Capital (R$)')
    axes[0,0].axhline(y=capital_inicial, color='black', linestyle='--', alpha=0.5, label='Capital Inicial')
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars1, capitals_finais):
        axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                      f'R$ {valor:.0f}', ha='center', va='bottom', fontweight='bold')
    
    axes[0,0].legend()
    
    # 2. Rendimento Percentual
    rendimentos_pct = [r['rendimento_percentual'] for r in resultados]
    bars2 = axes[0,1].bar(metodologias, rendimentos_pct, color=cores, alpha=0.7)
    axes[0,1].set_title('Rendimento Percentual')
    axes[0,1].set_ylabel('Rendimento (%)')
    axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars2, rendimentos_pct):
        axes[0,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + (1 if valor >= 0 else -3),
                      f'{valor:.1f}%', ha='center', va='bottom' if valor >= 0 else 'top', fontweight='bold')
    
    # 3. Número de Transações
    num_transacoes = [r['num_transacoes'] for r in resultados]
    bars3 = axes[1,0].bar(metodologias, num_transacoes, color=cores, alpha=0.7)
    axes[1,0].set_title('Número de Transações')
    axes[1,0].set_ylabel('Transações')
    
    # Adicionar valores nas barras
    for bar, valor in zip(bars3, num_transacoes):
        axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                      f'{valor}', ha='center', va='bottom', fontweight='bold')
    
    # 4. Evolução do Capital (se disponível)
    if all('historico_capital' in r for r in resultados):
        for i, resultado in enumerate(resultados):
            historico = pd.DataFrame(resultado['historico_capital'])
            if len(historico) > 0:
                axes[1,1].plot(historico['Data'], historico['Capital_Total'], 
                             label=resultado['metodologia'], color=cores[i], linewidth=2)
        
        axes[1,1].set_title('Evolução do Capital Total')
        axes[1,1].set_ylabel('Capital (R$)')
        axes[1,1].axhline(y=capital_inicial, color='black', linestyle='--', alpha=0.5, label='Capital Inicial')
        axes[1,1].legend()
        axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Salvar gráfico
    nome_arquivo = 'results/figures/comparacao_estrategias_investimento.png'
    os.makedirs(os.path.dirname(nome_arquivo), exist_ok=True)
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"   ✅ Gráfico salvo: {nome_arquivo}")

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    # Carregar configurações
    config_loader = ConfigLoader()
    capital_inicial = config_loader.get_initial_capital('comparison')
    periodo = config_loader.get_simulation_period('comparison')

    print("💰 COMPARAÇÃO DE ESTRATÉGIAS DE INVESTIMENTO")
    print("=" * 60)
    print(f"📊 Simulação: R$ {capital_inicial:.0f} iniciais por {periodo}")
    print("🔍 Metodologias: MM, Butterworth, XGBoost")
    print("=" * 60)
    
    # Carregar dados das 3 metodologias
    dados_mm = carregar_dados_mm()
    dados_butterworth = carregar_dados_butterworth()
    dados_xgboost = carregar_dados_xgboost()
    
    # Verificar se pelo menos uma metodologia tem dados
    dados_disponiveis = []
    if dados_mm is not None:
        dados_disponiveis.append(('MM', dados_mm))
    if dados_butterworth is not None:
        dados_disponiveis.append(('Butterworth', dados_butterworth))
    if dados_xgboost is not None:
        dados_disponiveis.append(('XGBoost', dados_xgboost))
    
    if not dados_disponiveis:
        print("❌ Nenhum dado encontrado. Execute primeiro os scripts de análise.")
        return
    
    print(f"\n📋 Dados disponíveis: {len(dados_disponiveis)} metodologias")
    
    # Simular investimento para cada metodologia
    resultados = []
    
    for metodologia, dados in dados_disponiveis:
        try:
            # Filtrar para período configurado
            dados_periodo, _, _ = filtrar_periodo_simulacao(dados)

            if len(dados_periodo) > 0:
                # Simular investimento
                resultado = simular_investimento(dados_periodo, metodologia)
                resultados.append(resultado)
            else:
                print(f"⚠️ Dados insuficientes para {metodologia}")
                
        except Exception as e:
            print(f"❌ Erro ao processar {metodologia}: {e}")
    
    if resultados:
        # Criar gráficos de comparação
        criar_graficos_comparacao(resultados)
        
        # Mostrar resumo final
        print(f"\n📋 RESUMO FINAL DA COMPARAÇÃO")
        print("=" * 60)
        
        melhor_rendimento = max(resultados, key=lambda x: x['rendimento_percentual'])
        
        for resultado in sorted(resultados, key=lambda x: x['rendimento_percentual'], reverse=True):
            status = "🏆" if resultado == melhor_rendimento else "📊"
            print(f"\n{status} {resultado['metodologia']}:")
            print(f"   💵 Capital Final: R$ {resultado['capital_final']:.2f}")
            print(f"   📈 Rendimento: R$ {resultado['rendimento_absoluto']:.2f} ({resultado['rendimento_percentual']:.2f}%)")
            print(f"   🔄 Transações: {resultado['num_transacoes']}")
            print(f"   📊 Posições Finais: {resultado['num_posicoes_finais']}")
        
        print(f"\n🏆 MELHOR ESTRATÉGIA: {melhor_rendimento['metodologia']}")
        print(f"📈 Rendimento Superior: {melhor_rendimento['rendimento_percentual']:.2f}%")
        
    else:
        print("❌ Nenhuma simulação foi executada com sucesso")

if __name__ == "__main__":
    main()
